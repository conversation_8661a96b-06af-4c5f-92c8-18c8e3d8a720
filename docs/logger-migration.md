# Logger Migration to go-utils/v5/log

This document describes the migration of the One API project's logging system from a custom logger implementation to the standardized `github.com/Laisky/go-utils/v5/log` logger.

## Overview

The project has been migrated to use `github.com/Laisky/go-utils/v5/log` as the underlying logging implementation while maintaining full backward compatibility with the existing logger API.

## Benefits

### 1. Seamless GMW Middleware Integration
The new logger integrates seamlessly with the `github.com/Laisky/gin-middlewares/v6` (gmw) middleware, allowing you to retrieve the logger directly from the gin context:

```go
import (
    gmw "github.com/Laisky/gin-middlewares/v6"
    "github.com/Laisky/zap"
    "github.com/gin-gonic/gin"
)

func RespondError(c *gin.Context, err error) {
    logger := gmw.GetLogger(c)
    logger.Error("HTTP server error", zap.Error(err))
    c.<PERSON>(http.StatusOK, gin.H{
        "success": false,
        "message": err.<PERSON>rror(),
    })
}
```

### 2. Structured Logging
The new logger provides structured logging capabilities with zap fields:

```go
import "github.com/Laisky/zap"

// Context-aware logging with structured fields
logger.Info(ctx, "User authenticated", 
    zap.String("user_id", userID),
    zap.String("method", "oauth"),
)
```

### 3. Better Performance
The go-utils logger is built on top of zap, providing better performance and lower memory allocation compared to the previous implementation.

### 4. Consistent Request Tracking
Request IDs are automatically extracted from context and included in log entries, providing better traceability across requests.

## API Compatibility

The migration maintains 100% backward compatibility. All existing logger functions continue to work exactly as before:

### System-Level Functions (No Context)
```go
logger.SysLog("System message")
logger.SysLogf("System message with %s", "formatting")
logger.SysWarn("System warning")
logger.SysWarnf("System warning with %s", "formatting")
logger.SysError("System error")
logger.SysErrorf("System error with %s", "formatting")
logger.FatalLog("Fatal error") // Calls os.Exit(1)
logger.FatalLogf("Fatal error with %s", "formatting")
```

### Context-Aware Functions
```go
logger.Debug(ctx, "Debug message")
logger.Info(ctx, "Info message")
logger.Warn(ctx, "Warning message")
logger.Error(ctx, "Error message")

logger.Debugf(ctx, "Debug message with %s", "formatting")
logger.Infof(ctx, "Info message with %s", "formatting")
logger.Warnf(ctx, "Warning message with %s", "formatting")
logger.Errorf(ctx, "Error message with %s", "formatting")
```

## Implementation Details

### Logger Initialization
The logger is automatically initialized on first use with the following configuration:
- **Name**: "one-api"
- **Level**: Info (or Debug if `config.DebugEnabled` is true)
- **Format**: Console format with colors
- **Features**: Hooks with fields enabled for better integration

### Request ID Extraction
When using context-aware logging functions, the logger automatically extracts request IDs from the context using `helper.GetRequestID(ctx)` and includes them as structured fields in the log output.

### File Logging
The existing file logging functionality is preserved through the `SetupLogger()` function, which configures gin's DefaultWriter and DefaultErrorWriter to write to log files when `LogDir` is specified.

## Migration Benefits for Developers

### 1. Enhanced Debugging
With gmw integration, you can now get request-scoped loggers that automatically include request context:

```go
func MyHandler(c *gin.Context) {
    logger := gmw.GetLogger(c)
    logger.Info("Processing request", zap.String("endpoint", c.Request.URL.Path))
    
    // The logger automatically includes request ID and other context
}
```

### 2. Better Error Handling
The new logger provides better error handling and structured error logging:

```go
func ProcessData(c *gin.Context, data []byte) error {
    logger := gmw.GetLogger(c)
    
    if err := validateData(data); err != nil {
        logger.Error("Data validation failed", 
            zap.Error(err),
            zap.Int("data_size", len(data)),
        )
        return err
    }
    
    logger.Info("Data processed successfully", zap.Int("data_size", len(data)))
    return nil
}
```

### 3. Consistent Logging Patterns
All logging now follows consistent patterns with proper structured fields and request tracing.

## Testing

The migration includes comprehensive tests that verify:
- All existing logger functions work correctly
- Context-aware logging extracts request IDs properly
- Debug flag handling works as expected
- Logger initialization is safe and idempotent
- Backward compatibility is maintained

Run the logger tests with:
```bash
go test -v ./common/logger
```

## Configuration

The logger respects the existing configuration:
- `config.DebugEnabled`: Controls debug log output
- `config.OnlyOneLogFile`: Controls log file rotation
- `LogDir`: Specifies log file directory

## Performance Considerations

The new logger implementation:
- Uses lazy initialization to avoid startup overhead
- Leverages zap's high-performance logging
- Minimizes memory allocations
- Provides efficient structured logging

## Future Enhancements

With the new logger foundation, future enhancements could include:
- Log level configuration per module
- Remote log shipping
- Metrics integration
- Advanced filtering and sampling
- Custom log formatters

## Troubleshooting

If you encounter any issues:

1. **Logger not initialized**: The logger initializes automatically on first use. If you need to force initialization, call `logger.SetupLogger()`.

2. **Missing request IDs**: Ensure you're using context-aware logging functions (`logger.Info(ctx, ...)`) rather than system-level functions (`logger.SysLog(...)`).

3. **Debug logs not appearing**: Check that `config.DebugEnabled` is set to `true`.

4. **File logging not working**: Verify that `LogDir` is set and the directory is writable.

## Conclusion

The migration to go-utils/v5/log provides a solid foundation for logging in the One API project while maintaining full backward compatibility. The integration with gmw middleware offers enhanced request tracing and structured logging capabilities that will improve debugging and monitoring capabilities.
