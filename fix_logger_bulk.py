#!/usr/bin/env python3

import os
import re
import sys

def fix_logger_calls(file_path):
    """Fix logger calls in a single file"""
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Pattern 1: logger.Logger.Error(fmt.Sprintf("message: %s", err.<PERSON>r()))
    # Replace with: logger.Logger.Error("message", zap.Error(err))
    pattern1 = r'logger\.Logger\.(Error|Warn|Info|Debug)\(fmt\.Sprintf\("([^"]*): %s", ([^)]+)\.Error\(\)\)\)'
    def replace1(match):
        level = match.group(1)
        message = match.group(2)
        error_var = match.group(3)
        return f'logger.Logger.{level}("{message}", zap.Error({error_var}))'
    content = re.sub(pattern1, replace1, content)
    
    # Pattern 2: logger.Logger.Error(fmt.Sprintf("message %d: %s", id, err.Error()))
    # Replace with: logger.Logger.Error("message", zap.Int("id", id), zap.Error(err))
    pattern2 = r'logger\.Logger\.(Error|Warn|Info|Debug)\(fmt\.Sprintf\("([^"]*) %d: %s", ([^,]+), ([^)]+)\.Error\(\)\)\)'
    def replace2(match):
        level = match.group(1)
        message = match.group(2)
        id_var = match.group(3)
        error_var = match.group(4)
        return f'logger.Logger.{level}("{message}", zap.Int("id", {id_var}), zap.Error({error_var}))'
    content = re.sub(pattern2, replace2, content)
    
    # Pattern 3: logger.Logger.Error(fmt.Sprintf("Channel %d message: %s", channel.Id, err.Error()))
    # Replace with: logger.Logger.Error("Channel message", zap.Int("channel_id", channel.Id), zap.Error(err))
    pattern3 = r'logger\.Logger\.(Error|Warn|Info|Debug)\(fmt\.Sprintf\("Channel %d ([^"]*): %s", ([^,]+), ([^)]+)\.Error\(\)\)\)'
    def replace3(match):
        level = match.group(1)
        message = match.group(2)
        id_var = match.group(3)
        error_var = match.group(4)
        return f'logger.Logger.{level}("Channel {message}", zap.Int("channel_id", {id_var}), zap.Error({error_var}))'
    content = re.sub(pattern3, replace3, content)
    
    # Pattern 4: logger.Logger.Info(fmt.Sprintf("message %d", count))
    # Replace with: logger.Logger.Info("message", zap.Int("count", count))
    pattern4 = r'logger\.Logger\.(Error|Warn|Info|Debug)\(fmt\.Sprintf\("([^"]*) %d", ([^)]+)\)\)'
    def replace4(match):
        level = match.group(1)
        message = match.group(2)
        var = match.group(3)
        return f'logger.Logger.{level}("{message}", zap.Int("count", {var}))'
    content = re.sub(pattern4, replace4, content)
    
    # Pattern 5: logger.Logger.Info(fmt.Sprintf("message %s", str))
    # Replace with: logger.Logger.Info("message", zap.String("value", str))
    pattern5 = r'logger\.Logger\.(Error|Warn|Info|Debug)\(fmt\.Sprintf\("([^"]*) %s", ([^)]+)\)\)'
    def replace5(match):
        level = match.group(1)
        message = match.group(2)
        var = match.group(3)
        return f'logger.Logger.{level}("{message}", zap.String("value", {var}))'
    content = re.sub(pattern5, replace5, content)
    
    # Check if we need to add zap import
    if content != original_content and 'zap.Error' in content and '"github.com/Laisky/zap"' not in content:
        # Add zap import
        import_pattern = r'(import \(\n(?:[^\)]*\n)*)'
        def add_zap_import(match):
            imports = match.group(1)
            if '"github.com/Laisky/zap"' not in imports:
                # Find the right place to insert zap import
                lines = imports.split('\n')
                inserted = False
                for i, line in enumerate(lines):
                    if 'github.com/Laisky/' in line and not inserted:
                        lines.insert(i+1, '\t"github.com/Laisky/zap"')
                        inserted = True
                        break
                if not inserted and len(lines) > 1:
                    lines.insert(-1, '\t"github.com/Laisky/zap"')
                return '\n'.join(lines)
            return imports
        content = re.sub(import_pattern, add_zap_import, content)
    
    # Write back if changed
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        return True
    return False

def main():
    if len(sys.argv) > 1:
        files = sys.argv[1:]
    else:
        # Find all Go files
        files = []
        for root, dirs, filenames in os.walk('.'):
            for filename in filenames:
                if filename.endswith('.go'):
                    files.append(os.path.join(root, filename))
    
    changed_files = []
    for file_path in files:
        try:
            if fix_logger_calls(file_path):
                changed_files.append(file_path)
                print(f"Fixed: {file_path}")
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    print(f"\nFixed {len(changed_files)} files")

if __name__ == "__main__":
    main()
