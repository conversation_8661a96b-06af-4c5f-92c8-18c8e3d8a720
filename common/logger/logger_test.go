package logger

import (
	"fmt"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"

	"github.com/songquanpeng/one-api/common/config"
)

func TestMain(m *testing.M) {
	// Set up test environment
	gin.SetMode(gin.TestMode)
	config.DebugEnabled = true

	// Run tests
	code := m.Run()

	// Clean up
	os.Exit(code)
}

func TestLoggerInitialization(t *testing.T) {
	// Test that logger initializes without panic
	require.NotPanics(t, func() {
		initLogger()
	})

	// Test that Logger is not nil after initialization
	require.NotNil(t, Logger)
}

func TestDirectLoggerFunctions(t *testing.T) {
	tests := []struct {
		name string
		fn   func()
	}{
		{
			name: "Info",
			fn:   func() { Logger.Info("test message") },
		},
		{
			name: "InfoFormatted",
			fn:   func() { Logger.Info(fmt.Sprintf("test message %s", "formatted")) },
		},
		{
			name: "Warn",
			fn:   func() { Logger.Warn("test warning") },
		},
		{
			name: "WarnFormatted",
			fn:   func() { Logger.Warn(fmt.Sprintf("test warning %s", "formatted")) },
		},
		{
			name: "Error",
			fn:   func() { Logger.Error("test error") },
		},
		{
			name: "ErrorFormatted",
			fn:   func() { Logger.Error(fmt.Sprintf("test error %s", "formatted")) },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.NotPanics(t, tt.fn)
		})
	}
}

func TestDirectLoggerWithDebug(t *testing.T) {
	// Save original debug state
	originalDebugEnabled := config.DebugEnabled
	defer func() {
		config.DebugEnabled = originalDebugEnabled
	}()

	tests := []struct {
		name string
		fn   func()
	}{
		{
			name: "Debug",
			fn:   func() { Logger.Debug("debug message") },
		},
		{
			name: "DebugFormatted",
			fn:   func() { Logger.Debug(fmt.Sprintf("debug message %s", "formatted")) },
		},
	}

	// Test with debug enabled
	config.DebugEnabled = true
	for _, tt := range tests {
		t.Run(tt.name+"_enabled", func(t *testing.T) {
			require.NotPanics(t, tt.fn)
		})
	}

	// Test with debug disabled
	config.DebugEnabled = false
	for _, tt := range tests {
		t.Run(tt.name+"_disabled", func(t *testing.T) {
			require.NotPanics(t, tt.fn)
		})
	}
}

func TestLoggerWithStructuredFields(t *testing.T) {
	// Test that logger works with structured fields
	require.NotPanics(t, func() {
		Logger.Info("test message with fields")
		Logger.Error("test error message")
		Logger.Warn("test warning message")
		Logger.Debug("test debug message")
	})
}

func TestSetupLogger(t *testing.T) {
	// Test that SetupLogger doesn't panic
	require.NotPanics(t, func() {
		SetupLogger()
	})

	// Test that it can be called multiple times safely
	require.NotPanics(t, func() {
		SetupLogger()
		SetupLogger()
	})
}

// TestLoggerIntegration tests the integration with the actual logging system
func TestLoggerIntegration(t *testing.T) {
	// This test ensures that the logger actually works end-to-end
	// We can't easily capture the output, but we can ensure no panics occur

	// Test all logging levels with direct logger usage
	require.NotPanics(t, func() {
		Logger.Debug("Integration test debug message")
		Logger.Info("Integration test info message")
		Logger.Warn("Integration test warn message")
		Logger.Error("Integration test error message")

		Logger.Debug(fmt.Sprintf("Integration test debug message with %s", "formatting"))
		Logger.Info(fmt.Sprintf("Integration test info message with %s", "formatting"))
		Logger.Warn(fmt.Sprintf("Integration test warn message with %s", "formatting"))
		Logger.Error(fmt.Sprintf("Integration test error message with %s", "formatting"))
	})
}
